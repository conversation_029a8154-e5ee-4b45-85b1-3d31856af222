# WebDAV 协议视频播放修复方案 (终极版)

## 🚨 问题根源分析

通过深入分析日志和WebDAV协议特性，发现问题的核心是：

### ProxyCache 与 WebDAV 协议冲突
```
E  ProxyCache error: Error opening connection for http://admin:<EMAIL>:5244/dav/%E5%A4%B8%E5%85%8B%E7%BD%91%E7%9B%98/...
E  HttpProxyCacheServer error: Error processing request
```

**根本原因**:
1. **GSYVideoPlayer内置ProxyCache**: 自动拦截HTTP请求进行本地缓存
2. **WebDAV需要Range请求**: 视频流播放需要HTTP Range请求支持分片下载
3. **认证头部丢失**: ProxyCache可能不正确转发WebDAV认证信息
4. **协议不兼容**: 缓存代理与WebDAV的文件访问模式冲突

## ✅ 终极解决方案

### 1. 应用级禁用缓存 - TVPlayerApplication.kt

**核心策略**: 在应用启动时彻底禁用GSYVideoPlayer的全局缓存机制

```kotlin
@HiltAndroidApp
class TVPlayerApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        // 禁用GSYVideoPlayer的全局缓存以支持WebDAV播放
        disableGSYVideoPlayerCache()
    }
    
    /**
     * 禁用GSYVideoPlayer的缓存机制，解决WebDAV播放问题
     */
    private fun disableGSYVideoPlayerCache() {
        try {
            // 禁用ProxyCache服务器
            val proxyCacheManagerClass = Class.forName("com.danikula.videocache.ProxyCacheManager")
            val isInstanceMethod = proxyCacheManagerClass.getMethod("isInstance")
            val isInstance = isInstanceMethod.invoke(null) as Boolean
            
            if (isInstance) {
                val shutdownMethod = proxyCacheManagerClass.getMethod("shutdown")
                shutdownMethod.invoke(null)
                Log.i("TVPlayerApplication", "ProxyCache disabled for WebDAV support")
            }
        } catch (e: Exception) {
            Log.w("TVPlayerApplication", "Could not disable ProxyCache, using alternative method: ${e.message}")
            
            // 替代方法：清除代理设置
            try {
                System.setProperty("http.proxyHost", "")
                System.setProperty("http.proxyPort", "")
                System.setProperty("https.proxyHost", "")
                System.setProperty("https.proxyPort", "")
                Log.i("TVPlayerApplication", "Proxy settings cleared for WebDAV support")
            } catch (ex: Exception) {
                Log.e("TVPlayerApplication", "Failed to configure proxy settings: ${ex.message}")
            }
        }
    }
}
```

### 2. 播放器级优化 - PlayerActivity.kt

**核心策略**: 
- 禁用单个播放器实例的缓存
- 配置WebDAV友好的HTTP头部
- 确保Range请求支持

```kotlin
@AndroidEntryPoint
class PlayerActivity : AppCompatActivity() {

    private lateinit var videoPlayer: StandardGSYVideoPlayer

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_player)

        videoPlayer = findViewById(R.id.video_player)

        val title = intent.getStringExtra(EXTRA_TITLE) ?: ""
        val uriString = intent.getStringExtra(EXTRA_URI)
        val uri = uriString?.let { Uri.parse(it) }

        if (uri == null) {
            Log.e("PlayerActivity", "No URI provided")
            finish()
            return
        }

        Log.d("PlayerActivity", "Playing video: $uri")
        
        // Setup GSYVideoPlayer with the URI - 禁用缓存以支持WebDAV
        videoPlayer.setUp(uri.toString(), false, title) // 第二个参数设为false禁用缓存
        
        // 为WebDAV播放配置特殊选项
        configureForWebDAV(uri)
        
        videoPlayer.startPlayLogic()
        
        // ... 其他初始化代码
    }
    
    /**
     * 为WebDAV播放配置特殊选项
     */
    private fun configureForWebDAV(uri: Uri) {
        try {
            if (isWebDAVUrl(uri)) {
                Log.d("PlayerActivity", "Configuring for WebDAV playback")
                
                // 禁用当前播放器的缓存服务器
                try {
                    val proxyCacheManagerClass = Class.forName("com.danikula.videocache.ProxyCacheManager")
                    val shutdownMethod = proxyCacheManagerClass.getMethod("shutdown")
                    shutdownMethod.invoke(null)
                    Log.d("PlayerActivity", "ProxyCache disabled for WebDAV")
                } catch (e: Exception) {
                    Log.w("PlayerActivity", "Could not disable ProxyCache: ${e.message}")
                }
                
                // 设置HTTP头部支持Range请求
                val headers = hashMapOf<String, String>(
                    "Accept-Ranges" to "bytes",
                    "Connection" to "keep-alive", 
                    "User-Agent" to "AndroidTVPlayer/1.0"
                )
                videoPlayer.setMapHeadData(headers)
                
                Log.d("PlayerActivity", "WebDAV headers configured: $headers")
            }
        } catch (e: Exception) {
            Log.e("PlayerActivity", "Error configuring WebDAV: ${e.message}", e)
        }
    }
    
    private fun isWebDAVUrl(uri: Uri): Boolean {
        val scheme = uri.scheme?.lowercase()
        return (scheme == "http" || scheme == "https") && 
               uri.host != null && 
               uri.host != "localhost" && 
               uri.host != "127.0.0.1"
    }
}
```

### 3. URL生成优化 - VideoDetailsFragment.kt

**保持之前的完整URL生成逻辑**，确保包含正确的认证信息和编码：

```kotlin
private fun startPlayback() {
    // ... 获取服务器配置
    
    // 生成带身份验证的完整WebDAV URL
    val webdavUrl = try {
        val baseUrl = server.url.removeSuffix("/")
        val normalizedPath = path.removePrefix("/")
        
        // 正确编码中文字符
        val encodedPath = java.net.URLEncoder.encode(normalizedPath, "UTF-8")
            .replace("+", "%20")
            .replace("%2F", "/")
        
        // 构建完整的认证URL
        val uri = java.net.URI(baseUrl)
        val scheme = uri.scheme
        val host = uri.host
        val port = if (uri.port != -1) ":${uri.port}" else ""
        val basePath = uri.path?.removeSuffix("/") ?: ""
        
        val fullUrl = "$scheme://${server.username}:${server.password}@$host$port$basePath/$encodedPath"
        
        Log.d("VideoDetailsFragment", "Generated authenticated WebDAV URL: $fullUrl")
        fullUrl
    } catch (e: Exception) {
        // 错误处理
        return
    }
    
    val uri = android.net.Uri.parse(webdavUrl)
    val intent = PlayerActivity.intentFor(requireContext(), mediaItem.getDisplayTitle(), uri)
    startActivity(intent)
}
```

## 🔧 技术原理

### WebDAV 协议要求
1. **HTTP Range 请求**: 用于视频分片播放
   ```
   Range: bytes=0-1023
   Accept-Ranges: bytes
   ```

2. **持久连接**: 保持连接以提高性能
   ```
   Connection: keep-alive
   ```

3. **认证透传**: 确保每个请求都包含认证信息
   ```
   Authorization: Basic <base64-encoded-credentials>
   ```

### ProxyCache 问题
1. **请求拦截**: 拦截原始HTTP请求
2. **本地代理**: 转发到`127.0.0.1:xxx`本地端口
3. **Range支持缺失**: 可能不正确处理Range请求头
4. **认证丢失**: 在代理过程中可能丢失认证信息

### 解决方案效果
- ✅ **直接连接**: 绕过ProxyCache，直接访问WebDAV服务器
- ✅ **Range支持**: 启用HTTP Range请求用于视频流
- ✅ **认证保持**: 确保每个请求都包含完整认证信息
- ✅ **中文支持**: 正确的UTF-8 URL编码

## 🎯 预期效果

修复后的播放流程：

1. **应用启动**: TVPlayerApplication禁用全局ProxyCache
2. **URL生成**: VideoDetailsFragment生成完整WebDAV URL
3. **播放器配置**: PlayerActivity禁用单实例缓存并配置WebDAV头部
4. **直接播放**: GSYVideoPlayer直接向WebDAV服务器发送HTTP Range请求
5. **成功流式播放**: 服务器返回视频分片，播放器正常播放

### 日志变化对比

**修复前 (失败)**:
```
E  ProxyCache error: Error opening connection for http://admin:<EMAIL>/dav/...
E  HttpProxyCacheServer error: Error processing request
```

**修复后 (成功)**:
```
D  PlayerActivity: Configuring for WebDAV playback
D  PlayerActivity: ProxyCache disabled for WebDAV
D  PlayerActivity: WebDAV headers configured
I  GSYVideoPlayer: Video prepared successfully
```

## 🧪 测试验证

### 验证要点
1. **缓存禁用**: 确认日志中没有ProxyCache相关错误
2. **直接连接**: 网络请求直接到WebDAV服务器，不经过本地代理
3. **Range请求**: 确认服务器收到正确的Range请求头
4. **认证成功**: 验证每个请求都包含认证信息
5. **播放流畅**: 视频能够正常加载和播放

### 测试用例
- ✅ 中文路径视频播放
- ✅ 大文件视频流式播放
- ✅ 网络切换场景
- ✅ 多次播放稳定性
- ✅ 不同视频格式兼容性

## 📝 关键改进点

### 1. 架构层面
- **应用级禁用**: 在Application中统一禁用缓存
- **播放器级优化**: 在播放器实例中进一步确保无缓存
- **协议适配**: 专门针对WebDAV协议的优化配置

### 2. 技术层面  
- **反射禁用**: 使用反射安全地禁用ProxyCache
- **HTTP头部**: 配置WebDAV友好的请求头
- **错误处理**: 完整的异常捕获和降级处理

### 3. 兼容性
- **优雅降级**: 如果禁用缓存失败，使用替代方案
- **多重保险**: 应用级+播放器级双重禁用
- **日志完善**: 详细的调试信息便于问题排查

---

**修复状态**: ✅ 已完成 (ProxyCache禁用 + WebDAV协议优化)  
**技术方案**: 多层缓存禁用 + 协议适配  
**测试状态**: 🔄 待验证  
**最后更新**: 2025-08-22 10:25

---

## 🚀 立即测试

现在请重新编译并测试视频播放功能。理论上应该能够成功播放WebDAV上的视频文件，包括中文路径的视频。如果还有问题，请提供新的日志信息。