<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 外层发光效果 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="18dp" />
            <stroke
                android:width="2dp"
                android:color="#40FFFFFF" />
        </shape>
    </item>
    
    <!-- 主要边框 -->
    <item android:inset="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="16dp" />
            <stroke
                android:width="4dp"
                android:color="@color/accent_color" />
        </shape>
    </item>
    
    <!-- 内层高亮 -->
    <item android:inset="6dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="#80FFFFFF" />
        </shape>
    </item>
    
</layer-list>
