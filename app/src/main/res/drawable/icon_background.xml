<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 获得焦点时 -->
    <item android:state_focused="true">
        <shape android:shape="oval">
            <solid android:color="@color/accent_color" />
        </shape>
    </item>
    
    <!-- 按下时 -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/primary_color" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#33FFFFFF" />
        </shape>
    </item>
    
</selector>
