<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="@color/accent_color" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="@color/text_secondary" />
        </shape>
    </item>
    
</selector>
