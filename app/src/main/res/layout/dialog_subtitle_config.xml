<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题栏 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="字幕设置"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="24dp"
            android:gravity="center" />

        <!-- 基本设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="基本设置"
            android:textColor="@color/accent_color"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <!-- 启用字幕 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="启用字幕"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_enabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 首选语言 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="首选语言"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <Spinner
                android:id="@+id/spinner_language"
                android:layout_width="120dp"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 自动下载 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="自动下载字幕"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_auto_download"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 显示设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="显示设置"
            android:textColor="@color/accent_color"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <!-- 字体大小 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="字体大小"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/text_size_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="16sp"
                android:textColor="@color/accent_color"
                android:textSize="14sp"
                android:minWidth="40dp"
                android:gravity="center" />

        </LinearLayout>

        <SeekBar
            android:id="@+id/seekbar_text_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- 字幕位置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="字幕位置"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <Spinner
                android:id="@+id/spinner_position"
                android:layout_width="100dp"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 对齐方式 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="对齐方式"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <Spinner
                android:id="@+id/spinner_alignment"
                android:layout_width="100dp"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 同步设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="同步设置"
            android:textColor="@color/accent_color"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <!-- 时间偏移 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="时间偏移"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/text_offset_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0ms"
                android:textColor="@color/accent_color"
                android:textSize="14sp"
                android:minWidth="60dp"
                android:gravity="center" />

        </LinearLayout>

        <SeekBar
            android:id="@+id/seekbar_offset"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp" />

        <!-- 预览 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="预览"
            android:textColor="@color/accent_color"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:background="@drawable/frosted_glass_background"
            android:layout_marginBottom="24dp">

            <TextView
                android:id="@+id/text_preview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:text="字幕预览效果\nSubtitle Preview Effect"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:gravity="center"
                android:padding="16dp" />

        </FrameLayout>

        <!-- 按钮组 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btn_reset"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="重置"
                android:background="@drawable/button_secondary"
                android:textColor="@color/white"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="取消"
                android:background="@drawable/button_secondary"
                android:textColor="@color/white"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="保存"
                android:background="@drawable/btn_play_background"
                android:textColor="@color/white" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>