<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black"
    android:padding="24dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="字幕选择"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="搜索"
            android:background="@drawable/button_secondary"
            android:textColor="@color/white"
            android:layout_marginEnd="8dp"
            android:minWidth="80dp" />

        <Button
            android:id="@+id/btn_config"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置"
            android:background="@drawable/button_secondary"
            android:textColor="@color/white"
            android:layout_marginEnd="8dp"
            android:minWidth="80dp" />

        <Button
            android:id="@+id/btn_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="关闭"
            android:background="@drawable/button_secondary"
            android:textColor="@color/white"
            android:minWidth="80dp" />

    </LinearLayout>

    <!-- 内容区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 字幕列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_subtitles"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp" />

        <!-- 空状态 -->
        <TextView
            android:id="@+id/text_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="暂无字幕\n点击搜索按钮查找字幕"
            android:textColor="@color/text_secondary"
            android:textSize="18sp"
            android:gravity="center"
            android:lineSpacingExtra="4dp"
            android:visibility="gone" />

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>

</LinearLayout>