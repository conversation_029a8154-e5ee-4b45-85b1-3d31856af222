<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:padding="32dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置"
            android:textSize="28sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_marginBottom="32dp" />

        <!-- 连接设置 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="连接设置"
            android:textSize="18sp"
            android:textColor="@color/accent_color"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_webdav_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="WebDAV服务器配置"
            android:textSize="16sp"
            android:backgroundTint="@color/primary_color"
            android:textColor="@color/white"
            android:layout_marginBottom="16dp"
            android:gravity="start|center_vertical"
            android:paddingStart="24dp"
            android:paddingEnd="24dp" />

        <Button
            android:id="@+id/btn_media_scan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="扫描媒体库"
            android:textSize="16sp"
            android:backgroundTint="@color/accent_color"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:gravity="start|center_vertical"
            android:paddingStart="24dp"
            android:paddingEnd="24dp" />

        <!-- 应用设置 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="应用设置"
            android:textSize="18sp"
            android:textColor="@color/accent_color"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_clear_cache"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="清除缓存"
            android:textSize="16sp"
            android:backgroundTint="@color/surface_color"
            android:textColor="@color/white"
            android:layout_marginBottom="16dp"
            android:gravity="start|center_vertical"
            android:paddingStart="24dp"
            android:paddingEnd="24dp" />

        <Button
            android:id="@+id/btn_playback_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="播放设置"
            android:textSize="16sp"
            android:backgroundTint="@color/surface_color"
            android:textColor="@color/white"
            android:layout_marginBottom="16dp"
            android:gravity="start|center_vertical"
            android:paddingStart="24dp"
            android:paddingEnd="24dp" />

        <!-- 关于 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="关于"
            android:textSize="18sp"
            android:textColor="@color/accent_color"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_about"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="关于应用"
            android:textSize="16sp"
            android:backgroundTint="@color/surface_color"
            android:textColor="@color/white"
            android:layout_marginBottom="16dp"
            android:gravity="start|center_vertical"
            android:paddingStart="24dp"
            android:paddingEnd="24dp" />

        <!-- 版本信息 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Android TV Player v1.0\nWebDAV视频播放器"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:alpha="0.7"
            android:layout_marginTop="32dp"
            android:gravity="center" />

    </LinearLayout>

</ScrollView>
