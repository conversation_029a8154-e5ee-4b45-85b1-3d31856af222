<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="wrap_content"
    android:background="@drawable/subtitle_control_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="字幕设置"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- 字体大小设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="字体大小"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/text_font_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="16sp"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:minWidth="50dp"
                android:gravity="end" />

        </LinearLayout>

        <SeekBar
            android:id="@+id/seekbar_font_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- 时间偏移设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="时间偏移"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/text_time_offset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0.0s"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:minWidth="50dp"
                android:gravity="end" />

        </LinearLayout>

        <SeekBar
            android:id="@+id/seekbar_time_offset"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- 字体颜色设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="字体颜色"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Spinner
                android:id="@+id/spinner_font_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="120dp" />

        </LinearLayout>

        <!-- 背景颜色设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="背景颜色"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Spinner
                android:id="@+id/spinner_background_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="120dp" />

        </LinearLayout>

        <!-- 位置设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="显示位置"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Spinner
                android:id="@+id/spinner_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="120dp" />

        </LinearLayout>

        <!-- 预览 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="预览"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:layout_marginBottom="8dp" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="#40000000"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/text_preview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="这是字幕预览效果"
                android:textColor="@android:color/white"
                android:textSize="16sp" />

        </FrameLayout>

        <!-- 按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/btn_reset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="重置"
                android:layout_marginEnd="8dp"
                android:focusable="true" />

            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消"
                android:layout_marginEnd="8dp"
                android:focusable="true" />

            <Button
                android:id="@+id/btn_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="确定"
                android:focusable="true" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
