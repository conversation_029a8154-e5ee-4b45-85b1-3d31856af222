<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|end"
    android:layout_margin="16dp"
    android:background="@drawable/subtitle_control_background"
    android:orientation="vertical"
    android:padding="8dp"
    android:visibility="gone">

    <!-- 字幕搜索按钮 -->
    <ImageButton
        android:id="@+id/btn_subtitle_search"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="搜索字幕"
        android:src="@drawable/ic_subtitle_search"
        android:focusable="true"
        android:nextFocusDown="@+id/btn_subtitle_toggle" />

    <!-- 字幕开关按钮 -->
    <ImageButton
        android:id="@+id/btn_subtitle_toggle"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="字幕开关"
        android:src="@drawable/ic_subtitle_off"
        android:focusable="true"
        android:nextFocusUp="@+id/btn_subtitle_search"
        android:nextFocusDown="@+id/btn_subtitle_settings" />

    <!-- 字幕设置按钮 -->
    <ImageButton
        android:id="@+id/btn_subtitle_settings"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="字幕设置"
        android:src="@drawable/ic_subtitle_settings"
        android:focusable="true"
        android:nextFocusUp="@+id/btn_subtitle_toggle"
        android:nextFocusDown="@+id/btn_subtitle_sync" />

    <!-- 字幕同步按钮 -->
    <ImageButton
        android:id="@+id/btn_subtitle_sync"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="字幕同步"
        android:src="@drawable/ic_subtitle_sync"
        android:focusable="true"
        android:nextFocusUp="@+id/btn_subtitle_settings"
        android:nextFocusDown="@+id/btn_subtitle_track" />

    <!-- 字幕轨道选择按钮 -->
    <ImageButton
        android:id="@+id/btn_subtitle_track"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="字幕轨道"
        android:src="@drawable/ic_subtitle_track"
        android:focusable="true"
        android:nextFocusUp="@+id/btn_subtitle_sync" />

    <!-- 字幕时间偏移控制 -->
    <LinearLayout
        android:id="@+id/layout_subtitle_offset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageButton
            android:id="@+id/btn_subtitle_offset_minus"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="字幕延迟"
            android:src="@drawable/ic_remove"
            android:focusable="true" />

        <TextView
            android:id="@+id/tv_subtitle_offset"
            android:layout_width="60dp"
            android:layout_height="36dp"
            android:gravity="center"
            android:text="0.0s"
            android:textColor="@android:color/white"
            android:textSize="12sp" />

        <ImageButton
            android:id="@+id/btn_subtitle_offset_plus"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="字幕提前"
            android:src="@drawable/ic_add"
            android:focusable="true" />

    </LinearLayout>

</LinearLayout>
